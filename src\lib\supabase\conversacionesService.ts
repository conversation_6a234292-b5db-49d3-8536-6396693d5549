import { supabase, Conversacion, Mensaje } from './supabaseClient';

/**
 * Crea una nueva conversación
 * @param titulo Título de la conversación
 * @param activa Si la conversación debe marcarse como activa
 */
export async function crearConversacion(titulo: string, activa: boolean = false): Promise<string | null> {
  try {
    // Si la conversación va a ser activa, primero desactivamos todas las demás
    if (activa) {
      await desactivarTodasLasConversaciones();
    }

    const { data, error } = await supabase
      .from('conversaciones')
      .insert([{ titulo, activa }])
      .select();

    if (error) {
      console.error('Error al crear conversación:', error);
      return null;
    }

    return data?.[0]?.id || null;
  } catch (error) {
    console.error('Error inesperado al crear conversación:', error);
    return null;
  }
}

/**
 * Obtiene todas las conversaciones ordenadas por fecha de actualización
 */
export async function obtenerConversaciones(): Promise<Conversacion[]> {
  try {
    // Obtener el usuario actual
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      console.error('No hay usuario autenticado para obtener conversaciones');
      return [];
    }

    const { data, error } = await supabase
      .from('conversaciones')
      .select('*')
      .eq('user_id', user.id)
      .order('actualizado_en', { ascending: false });

    if (error) {
      console.error('Error al obtener conversaciones:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error inesperado al obtener conversaciones:', error);
    return [];
  }
}

/**
 * Obtiene una conversación específica por su ID
 */
export async function obtenerConversacionPorId(id: string): Promise<Conversacion | null> {
  const { data, error } = await supabase
    .from('conversaciones')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error al obtener conversación:', error);
    return null;
  }

  return data;
}

/**
 * Actualiza el título de una conversación
 */
export async function actualizarConversacion(id: string, titulo: string): Promise<boolean> {
  const { error } = await supabase
    .from('conversaciones')
    .update({ titulo, actualizado_en: new Date().toISOString() })
    .eq('id', id);

  if (error) {
    console.error('Error al actualizar conversación:', error);
    return false;
  }

  return true;
}

/**
 * Marca una conversación como activa y desactiva todas las demás
 */
export async function activarConversacion(id: string): Promise<boolean> {
  try {
    // Primero desactivamos todas las conversaciones
    await desactivarTodasLasConversaciones();

    // Luego activamos la conversación específica
    const { error } = await supabase
      .from('conversaciones')
      .update({ activa: true, actualizado_en: new Date().toISOString() })
      .eq('id', id);

    if (error) {
      console.error('Error al activar conversación:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error inesperado al activar conversación:', error);
    return false;
  }
}

/**
 * Desactiva todas las conversaciones
 */
export async function desactivarTodasLasConversaciones(): Promise<boolean> {
  try {
    // Obtener el usuario actual
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      console.error('No hay usuario autenticado para desactivar conversaciones');
      return false;
    }

    // Desactivar todas las conversaciones del usuario actual
    const { error } = await supabase
      .from('conversaciones')
      .update({ activa: false })
      .eq('user_id', user.id)
      .eq('activa', true); // Solo actualizar las que están activas

    if (error) {
      console.error('Error al desactivar todas las conversaciones:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error inesperado al desactivar conversaciones:', error);
    return false;
  }
}

/**
 * Obtiene la conversación activa actual
 */
export async function obtenerConversacionActiva(): Promise<Conversacion | null> {
  try {
    // Obtener el usuario actual
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      console.error('No hay usuario autenticado para obtener conversación activa');
      return null;
    }

    const { data, error } = await supabase
      .from('conversaciones')
      .select('*')
      .eq('activa', true)
      .eq('user_id', user.id)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 es el código cuando no se encuentra ningún registro
      console.error('Error al obtener conversación activa:', error);
      return null;
    }

    return data || null;
  } catch (error) {
    console.error('Error inesperado al obtener conversación activa:', error);
    return null;
  }
}

/**
 * Guarda un mensaje en la base de datos
 */
export async function guardarMensaje(mensaje: Omit<Mensaje, 'id' | 'timestamp'>): Promise<string | null> {
  try {
    // Primero verificamos que la conversación exista
    const { data: conversacion, error: errorConversacion } = await supabase
      .from('conversaciones')
      .select('id')
      .eq('id', mensaje.conversacion_id)
      .single();

    if (errorConversacion) {
      console.error('Error al verificar la conversación:', errorConversacion);
      return null;
    }

    // Guardar el mensaje
    const { data, error } = await supabase
      .from('mensajes')
      .insert([mensaje])
      .select();

    if (error) {
      console.error('Error al guardar mensaje:', error);
      return null;
    }

    // Actualizar la fecha de la conversación
    const { error: errorActualizacion } = await supabase
      .from('conversaciones')
      .update({ actualizado_en: new Date().toISOString() })
      .eq('id', mensaje.conversacion_id);

    if (errorActualizacion) {
      console.error('Error al actualizar la fecha de la conversación:', errorActualizacion);
      // No retornamos error aquí porque el mensaje ya se guardó correctamente
    }

    return data?.[0]?.id || null;
  } catch (error) {
    console.error('Error inesperado al guardar mensaje:', error);
    return null;
  }
}

/**
 * Obtiene todos los mensajes de una conversación
 */
export async function obtenerMensajesPorConversacionId(conversacionId: string): Promise<Mensaje[]> {
  const { data, error } = await supabase
    .from('mensajes')
    .select('*')
    .eq('conversacion_id', conversacionId)
    .order('timestamp', { ascending: true });

  if (error) {
    console.error('Error al obtener mensajes:', error);
    return [];
  }

  return data || [];
}
