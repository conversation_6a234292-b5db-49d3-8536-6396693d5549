import { model, prepararDocumentos } from './geminiClient';
import { PROMPT_MAPAS_MENTALES } from '../../config/prompts';

/**
 * Genera un mapa mental a partir de los documentos
 */
export async function generarMapaMental(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  instrucciones?: string
): Promise<any> {
  try {
    // Preparar el contenido de los documentos
    const contenidoDocumentos = prepararDocumentos(documentos);

    if (!contenidoDocumentos) {
      throw new Error("No se han proporcionado documentos para generar el mapa mental.");
    }

    // Construir el prompt para la IA usando el prompt personalizado
    // Reemplazar las variables en el prompt
    let prompt = PROMPT_MAPAS_MENTALES
      .replace('{documentos}', contenidoDocumentos);

    // Añadir instrucciones adicionales si se proporcionan
    if (instrucciones) {
      prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\n- ${instrucciones}`);
    } else {
      prompt = prompt.replace('{instrucciones}', '');
    }

    // Generar el mapa mental
    const result = await model.generateContent(prompt);
    const response = result.response.text();

    // Extraer el HTML completo de la respuesta (el prompt genera HTML completo)
    // El mapa mental se genera como HTML con D3.js, no como JSON

    // Verificar que la respuesta contiene HTML válido
    if (!response.includes('<!DOCTYPE html>') && !response.includes('<html')) {
      console.error('Respuesta de Gemini para mapa mental:', response);
      throw new Error("La respuesta no contiene HTML válido para el mapa mental.");
    }

    // Retornar el HTML completo como string
    return response;
  } catch (error) {
    console.error('Error al generar mapa mental:', error);
    throw error;
  }
}
