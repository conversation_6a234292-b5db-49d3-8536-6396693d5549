/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CJORGE%5CDesktop%5Ctrabajo%5Coposiciones%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJORGE%5CDesktop%5Ctrabajo%5Coposiciones&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CJORGE%5CDesktop%5Ctrabajo%5Coposiciones%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJORGE%5CDesktop%5Ctrabajo%5Coposiciones&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(rsc)/./src/app/login/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CJORGE%5CDesktop%5Ctrabajo%5Coposiciones%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJORGE%5CDesktop%5Ctrabajo%5Coposiciones&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Csrc%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Csrc%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientLayout.tsx */ \"(rsc)/./src/components/ClientLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0pPUkdFJTVDJTVDRGVza3RvcCU1QyU1Q3RyYWJham8lNUMlNUNvcG9zaWNpb25lcyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNKT1JHRSU1QyU1Q0Rlc2t0b3AlNUMlNUN0cmFiYWpvJTVDJTVDb3Bvc2ljaW9uZXMlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNKT1JHRSU1QyU1Q0Rlc2t0b3AlNUMlNUN0cmFiYWpvJTVDJTVDb3Bvc2ljaW9uZXMlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDQ2xpZW50TGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDhLQUFvSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXEpPUkdFXFxcXERlc2t0b3BcXFxcdHJhYmFqb1xcXFxvcG9zaWNpb25lc1xcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxDbGllbnRMYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Csrc%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Csrc%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Csrc%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientLayout.tsx */ \"(ssr)/./src/components/ClientLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0pPUkdFJTVDJTVDRGVza3RvcCU1QyU1Q3RyYWJham8lNUMlNUNvcG9zaWNpb25lcyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNKT1JHRSU1QyU1Q0Rlc2t0b3AlNUMlNUN0cmFiYWpvJTVDJTVDb3Bvc2ljaW9uZXMlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNKT1JHRSU1QyU1Q0Rlc2t0b3AlNUMlNUN0cmFiYWpvJTVDJTVDb3Bvc2ljaW9uZXMlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDQ2xpZW50TGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDhLQUFvSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXEpPUkdFXFxcXERlc2t0b3BcXFxcdHJhYmFqb1xcXFxvcG9zaWNpb25lc1xcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxDbGllbnRMYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Csrc%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(rsc)/./src/app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0pPUkdFJTVDJTVDRGVza3RvcCU1QyU1Q3RyYWJham8lNUMlNUNvcG9zaWNpb25lcyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2xvZ2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUErRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcSk9SR0VcXFxcRGVza3RvcFxcXFx0cmFiYWpvXFxcXG9wb3NpY2lvbmVzXFxcXHNyY1xcXFxhcHBcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(ssr)/./src/app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0pPUkdFJTVDJTVDRGVza3RvcCU1QyU1Q3RyYWJham8lNUMlNUNvcG9zaWNpb25lcyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2xvZ2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUErRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcSk9SR0VcXFxcRGVza3RvcFxcXFx0cmFiYWpvXFxcXG9wb3NpY2lvbmVzXFxcXHNyY1xcXFxhcHBcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJORGE%5C%5CDesktop%5C%5Ctrabajo%5C%5Coposiciones%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction LoginPage() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [formError, setFormError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { iniciarSesion, error, isLoading, user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // El middleware y AuthContext ya manejan la redirección si hay una sesión activa\n    // No necesitamos un useEffect adicional para esto\n    // Actualizar el error del formulario cuando cambia el error de autenticación\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            if (error) {\n                // Los mensajes de error ya vienen traducidos desde authService.ts\n                setFormError(error);\n                // Si es un error de sincronización de tiempo, mostrar información adicional\n                if (error.includes('sincronización de tiempo')) {\n                    console.info('Sugerencia: Verifica que la hora de tu dispositivo esté correctamente configurada y sincronizada con un servidor de tiempo.');\n                }\n            }\n        }\n    }[\"LoginPage.useEffect\"], [\n        error\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setFormError('');\n        // Validaciones básicas\n        if (!email.trim()) {\n            setFormError('Por favor, ingresa tu email');\n            return;\n        }\n        if (!password.trim()) {\n            setFormError('Por favor, ingresa tu contraseña');\n            return;\n        }\n        // Intentar iniciar sesión\n        await iniciarSesion(email, password);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                        children: \"OposiAI\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"mt-2 text-center text-xl font-semibold text-gray-900\",\n                        children: \"Inicia sesi\\xf3n en tu cuenta\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-center text-sm text-gray-600\",\n                        children: \"Accede a tu asistente inteligente para oposiciones\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"space-y-6\",\n                            onSubmit: handleSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"email\",\n                                                name: \"email\",\n                                                type: \"email\",\n                                                autoComplete: \"email\",\n                                                required: true,\n                                                value: email,\n                                                onChange: (e)=>setEmail(e.target.value),\n                                                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                                                disabled: isLoading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Contrase\\xf1a\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"password\",\n                                                name: \"password\",\n                                                type: \"password\",\n                                                autoComplete: \"current-password\",\n                                                required: true,\n                                                value: password,\n                                                onChange: (e)=>setPassword(e.target.value),\n                                                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                                                disabled: isLoading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                formError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-500 text-sm bg-red-50 p-3 rounded-md border border-red-200\",\n                                    children: [\n                                        formError,\n                                        formError.includes('sincronización de tiempo') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 text-gray-600 text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Sugerencia:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 24\n                                                        }, this),\n                                                        \" Este error puede ocurrir cuando la hora de tu dispositivo no est\\xe1 sincronizada correctamente.\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                    className: \"list-decimal pl-5 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Verifica que la fecha y hora de tu dispositivo est\\xe9n configuradas correctamente\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 111,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Activa la sincronizaci\\xf3n autom\\xe1tica de hora en tu sistema\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Reinicia el navegador e intenta nuevamente\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isLoading,\n                                        className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            className: \"opacity-25\",\n                                                            cx: \"12\",\n                                                            cy: \"12\",\n                                                            r: \"10\",\n                                                            stroke: \"currentColor\",\n                                                            strokeWidth: \"4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            className: \"opacity-75\",\n                                                            fill: \"currentColor\",\n                                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Iniciando sesi\\xf3n...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 19\n                                        }, this) : 'Iniciar sesión'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-center text-sm text-gray-600\",\n                                children: \"\\xbfNo tienes una cuenta? Contacta con el administrador para solicitar acceso.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AuthManager.tsx":
/*!****************************************!*\
  !*** ./src/components/AuthManager.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthManager)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n/**\n * Componente para manejar errores comunes de autenticación\n * y sincronización de tiempo en Supabase\n */ function AuthManager() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"AuthManager.useEffect\": ()=>{\n            // Verificar si hay problemas de sincronización de tiempo\n            const checkTimeSync = {\n                \"AuthManager.useEffect.checkTimeSync\": async ()=>{\n                    try {\n                        // Obtener la hora del servidor de Supabase\n                        const response = await fetch('https://fxnhpxjijinfuxxxplzj.supabase.co/rest/v1/', {\n                            method: 'GET',\n                            headers: {\n                                'Content-Type': 'application/json',\n                                'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4'\n                            }\n                        });\n                        // Obtener la fecha del servidor desde las cabeceras\n                        const serverDate = new Date(response.headers.get('date') || '');\n                        const clientDate = new Date();\n                        // Calcular la diferencia en segundos\n                        const timeDiff = Math.abs((serverDate.getTime() - clientDate.getTime()) / 1000);\n                        // Si la diferencia es mayor a 60 segundos, mostrar una advertencia\n                        if (timeDiff > 60) {\n                            console.warn(`Posible problema de sincronización de tiempo detectado. ` + `La diferencia entre tu hora local y el servidor es de ${Math.round(timeDiff)} segundos. ` + `Esto puede causar problemas de autenticación.`);\n                        }\n                    } catch (error) {\n                        console.error('Error al verificar sincronización de tiempo:', error);\n                    }\n                }\n            }[\"AuthManager.useEffect.checkTimeSync\"];\n            // Ejecutar la verificación\n            checkTimeSync();\n            // Configurar un listener para eventos de autenticación\n            const { data: authListener } = _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.onAuthStateChange({\n                \"AuthManager.useEffect\": (event, session)=>{\n                    console.log('[AuthManager] Auth event:', event, 'Session:', !!session);\n                    if (event === 'SIGNED_OUT') {\n                        // Supabase ya maneja la limpieza de tokens internamente\n                        console.log('[AuthManager] Sesión cerrada');\n                    } else if (event === 'SIGNED_IN') {\n                        console.log('[AuthManager] Sesión iniciada correctamente');\n                    }\n                }\n            }[\"AuthManager.useEffect\"]);\n            return ({\n                \"AuthManager.useEffect\": ()=>{\n                    authListener.subscription.unsubscribe();\n                }\n            })[\"AuthManager.useEffect\"];\n        }\n    }[\"AuthManager.useEffect\"], []);\n    // Este componente no renderiza nada visible\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AuthManager.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ClientLayout.tsx":
/*!*****************************************!*\
  !*** ./src/components/ClientLayout.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_AuthManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthManager */ \"(ssr)/./src/components/AuthManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ClientLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\components\\\\ClientLayout.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\components\\\\ClientLayout.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9DbGllbnRMYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRTBCO0FBQzRCO0FBQ0g7QUFNcEMsU0FBU0csYUFBYSxFQUFFQyxRQUFRLEVBQXFCO0lBQ2xFLHFCQUNFLDhEQUFDSCwrREFBWUE7OzBCQUNYLDhEQUFDQywrREFBV0E7Ozs7O1lBQ1hFOzs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSk9SR0VcXERlc2t0b3BcXHRyYWJham9cXG9wb3NpY2lvbmVzXFxzcmNcXGNvbXBvbmVudHNcXENsaWVudExheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5pbXBvcnQgQXV0aE1hbmFnZXIgZnJvbSAnQC9jb21wb25lbnRzL0F1dGhNYW5hZ2VyJztcblxuaW50ZXJmYWNlIENsaWVudExheW91dFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQ2xpZW50TGF5b3V0KHsgY2hpbGRyZW4gfTogQ2xpZW50TGF5b3V0UHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgPEF1dGhNYW5hZ2VyIC8+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9BdXRoUHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJBdXRoUHJvdmlkZXIiLCJBdXRoTWFuYWdlciIsIkNsaWVudExheW91dCIsImNoaWxkcmVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ClientLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase/authService */ \"(ssr)/./src/lib/supabase/authService.ts\");\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); // Start true: loading initial auth state\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Effect for auth state listener and initial session check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setIsLoading(true); // Explicitly set loading true at the start of auth setup\n            const { data: authListener } = _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": (event, currentSession)=>{\n                    setSession(currentSession);\n                    setUser(currentSession?.user ?? null);\n                    setError(null); // Clear previous errors on any auth state change\n                    // Centralize setIsLoading(false) after processing the event.\n                    if (event === 'INITIAL_SESSION' || event === 'SIGNED_IN' || event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED' || event === 'USER_UPDATED' || event === 'PASSWORD_RECOVERY') {\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect\"]);\n            // Initial session fetch. onAuthStateChange with INITIAL_SESSION will also fire.\n            _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession().then({\n                \"AuthProvider.useEffect\": ({ data: { session: initialSessionCheck }, error: getSessionError })=>{\n                    if (getSessionError) {\n                        setError(getSessionError.message);\n                        setIsLoading(false); // Ensure loading is false if initial getSession fails\n                    }\n                    // Para dispositivos móviles, verificar también localStorage si no hay sesión\n                    if (!initialSessionCheck && \"undefined\" !== 'undefined') {}\n                // If INITIAL_SESSION hasn't fired and set loading to false, and this fails, we ensure it's false.\n                // Note: if getSession is successful, `setIsLoading(false)` is primarily handled by INITIAL_SESSION event.\n                }\n            }[\"AuthProvider.useEffect\"]).catch({\n                \"AuthProvider.useEffect\": (error)=>{\n                    setError(error.message);\n                    setIsLoading(false); // Ensure loading is false if initial getSession throws\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    authListener?.subscription.unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []); // Runs once on mount\n    // Effect for handling redirections based on auth state and pathname\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // No realizar redirecciones mientras se está cargando\n            if (isLoading) {\n                return;\n            }\n            // No aplicar redirecciones a rutas de API o recursos estáticos\n            if (pathname.startsWith('/api') || pathname.startsWith('/_next')) {\n                return;\n            }\n            // Definir rutas públicas (mantener sincronizado con middleware)\n            const publicPaths = [\n                '/login'\n            ];\n            // Si hay sesión y estamos en /login, el middleware ya debería haber redirigido.\n            // Esta es una salvaguarda del lado del cliente.\n            if (session && pathname === '/login') {\n                router.replace('/'); // router.replace es mejor aquí para evitar entradas en el historial\n                return; // Importante retornar para no evaluar la siguiente condición\n            }\n            // Si NO hay sesión y NO estamos en una ruta pública (y no es una ruta API/interna)\n            // Esta lógica es para cuando el estado cambia en el cliente (ej. logout)\n            if (!session && !publicPaths.includes(pathname) && !pathname.startsWith('/api') && !pathname.startsWith('/_next')) {\n                router.replace('/login');\n                return;\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        session,\n        isLoading,\n        pathname,\n        router\n    ]);\n    const iniciarSesion = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[iniciarSesion]\": async (email, password_provided)=>{\n            setIsLoading(true);\n            setError(null);\n            try {\n                const { user: loggedInUser, session: currentAuthSession, error: loginError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__.iniciarSesion)(email, password_provided);\n                if (loginError) {\n                    setError(loginError);\n                    setIsLoading(false); // Ensure loading is false on error\n                    return {\n                        user: null,\n                        session: null,\n                        error: loginError\n                    };\n                }\n                // Verificar que la sesión se haya establecido correctamente antes de redirigir\n                if (currentAuthSession) {\n                    // Esperar un momento adicional para asegurar que las cookies se propaguen\n                    // antes de la redirección\n                    await new Promise({\n                        \"AuthProvider.useCallback[iniciarSesion]\": (resolve)=>setTimeout(resolve, 300)\n                    }[\"AuthProvider.useCallback[iniciarSesion]\"]);\n                    // Redirigir a la página principal usando replace para evitar entradas en el historial\n                    router.replace('/');\n                }\n                // If successful, onAuthStateChange (SIGNED_IN) will set user, session, and isLoading to false.\n                return {\n                    user: loggedInUser,\n                    session: currentAuthSession,\n                    error: null\n                };\n            } catch (e) {\n                const errorMessage = e instanceof Error && e.message ? e.message : 'Error desconocido durante el inicio de sesión.';\n                setError(errorMessage);\n                setIsLoading(false); // Ensure loading is false on exception\n                return {\n                    user: null,\n                    session: null,\n                    error: errorMessage\n                };\n            }\n        }\n    }[\"AuthProvider.useCallback[iniciarSesion]\"], [\n        router\n    ]); // Added router dependency\n    const cerrarSesion = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[cerrarSesion]\": async ()=>{\n            setIsLoading(true);\n            setError(null);\n            const { error: logoutError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__.cerrarSesion)();\n            if (logoutError) {\n                setError(logoutError);\n                setIsLoading(false); // Ensure loading is false on error\n            }\n        // If successful, onAuthStateChange (SIGNED_OUT) handles state updates and isLoading.\n        // The redirection useEffect will then handle redirecting to /login.\n        }\n    }[\"AuthProvider.useCallback[cerrarSesion]\"], []); // Assuming cerrarSesionService is a stable import\n    const estaAutenticado = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[estaAutenticado]\": ()=>!!user && !!session && !isLoading\n    }[\"AuthProvider.useCallback[estaAutenticado]\"], [\n        user,\n        session,\n        isLoading\n    ]);\n    const value = {\n        user,\n        session,\n        isLoading,\n        error,\n        iniciarSesion,\n        cerrarSesion,\n        estaAutenticado\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 181,\n        columnNumber: 10\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth debe ser utilizado dentro de un AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/authService.ts":
/*!*****************************************!*\
  !*** ./src/lib/supabase/authService.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cerrarSesion: () => (/* binding */ cerrarSesion),\n/* harmony export */   estaAutenticado: () => (/* binding */ estaAutenticado),\n/* harmony export */   iniciarSesion: () => (/* binding */ iniciarSesion),\n/* harmony export */   obtenerSesion: () => (/* binding */ obtenerSesion),\n/* harmony export */   obtenerUsuarioActual: () => (/* binding */ obtenerUsuarioActual)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n\n/**\n * Inicia sesión con email y contraseña\n */ async function iniciarSesion(email, password) {\n    try {\n        // Verificar que el email y la contraseña no estén vacíos\n        if (!email || !password) {\n            return {\n                user: null,\n                session: null,\n                error: 'Por favor, ingresa tu email y contraseña'\n            };\n        }\n        // No cerramos la sesión antes de iniciar una nueva, esto causa un ciclo\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email: email.trim(),\n            password: password\n        });\n        if (error) {\n            // Manejar específicamente el error de sincronización de tiempo\n            if (error.message.includes('issued in the future') || error.message.includes('clock for skew')) {\n                return {\n                    user: null,\n                    session: null,\n                    error: 'Error de sincronización de tiempo. Por favor, verifica que la hora de tu dispositivo esté correctamente configurada.'\n                };\n            }\n            // Manejar error de credenciales inválidas de forma más amigable\n            if (error.message.includes('Invalid login credentials')) {\n                return {\n                    user: null,\n                    session: null,\n                    error: 'Email o contraseña incorrectos. Por favor, verifica tus credenciales.'\n                };\n            }\n            return {\n                user: null,\n                session: null,\n                error: error.message\n            }; // Added session\n        }\n        // Ensure data.user and data.session exist before returning\n        if (data && data.user && data.session) {\n            // Esperar un momento para asegurar que las cookies se establezcan\n            // Esto es importante para que el middleware pueda detectar la sesión\n            await new Promise((resolve)=>setTimeout(resolve, 800));\n            // Verificar que la sesión esté disponible después de establecer las cookies\n            await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            return {\n                user: data.user,\n                session: data.session,\n                error: null\n            }; // Added session\n        } else {\n            // This case should ideally not be reached if Supabase call is successful\n            // but provides a fallback if data or its properties are unexpectedly null/undefined.\n            return {\n                user: null,\n                session: null,\n                error: 'Respuesta inesperada del servidor al iniciar sesión.'\n            };\n        }\n    } catch (e) {\n        // Check if 'e' is an Error object and has a message property\n        const errorMessage = e instanceof Error && e.message ? e.message : 'Ha ocurrido un error inesperado al iniciar sesión';\n        return {\n            user: null,\n            session: null,\n            error: errorMessage\n        };\n    }\n}\n/**\n * Cierra la sesión del usuario actual\n */ async function cerrarSesion() {\n    try {\n        const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut();\n        if (error) {\n            return {\n                error: error.message\n            };\n        }\n        return {\n            error: null\n        };\n    } catch (error) {\n        return {\n            error: 'Ha ocurrido un error inesperado al cerrar sesión'\n        };\n    }\n}\n/**\n * Obtiene la sesión actual del usuario\n */ async function obtenerSesion() {\n    try {\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    session: null,\n                    error: null\n                };\n            }\n            return {\n                session: null,\n                error: error.message\n            };\n        }\n        return {\n            session: data.session,\n            error: null\n        };\n    } catch (error) {\n        return {\n            session: null,\n            error: 'Ha ocurrido un error inesperado al obtener la sesión'\n        };\n    }\n}\n/**\n * Obtiene el usuario actual\n */ async function obtenerUsuarioActual() {\n    try {\n        const { data: { user }, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    user: null,\n                    error: null\n                };\n            }\n            return {\n                user: null,\n                error: error.message\n            };\n        }\n        return {\n            user,\n            error: null\n        };\n    } catch (error) {\n        return {\n            user: null,\n            error: 'Ha ocurrido un error inesperado al obtener el usuario actual'\n        };\n    }\n}\n/**\n * Verifica si el usuario está autenticado\n */ async function estaAutenticado() {\n    const { session } = await obtenerSesion();\n    return session !== null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/authService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/supabaseClient.ts":
/*!********************************************!*\
  !*** ./src/lib/supabase/supabaseClient.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSupabaseClient: () => (/* binding */ getSupabaseClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Configuración del cliente de Supabase\nconst supabaseUrl = 'https://fxnhpxjijinfuxxxplzj.supabase.co';\nconst supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4';\n// Opciones adicionales para el cliente de Supabase\nconst supabaseOptions = {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true,\n        // Configuración mejorada para dispositivos móviles\n        storageKey: 'supabase.auth.token',\n        storage: {\n            getItem: (key)=>{\n                if (false) {}\n                return null;\n            },\n            setItem: (key, value)=>{\n                if (false) {}\n            },\n            removeItem: (key)=>{\n                if (false) {}\n            }\n        }\n    }\n};\n// Implementar patrón singleton para evitar múltiples instancias\nlet supabaseInstance = null;\nconst getSupabaseClient = ()=>{\n    if (supabaseInstance === null) {\n        supabaseInstance = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, supabaseOptions);\n    }\n    return supabaseInstance;\n};\n// Exportar una instancia única del cliente de Supabase\nconst supabase = getSupabaseClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/supabaseClient.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d88dc6b14989\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEpPUkdFXFxEZXNrdG9wXFx0cmFiYWpvXFxvcG9zaWNpb25lc1xcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZDg4ZGM2YjE0OTg5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_ClientLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ClientLayout */ \"(rsc)/./src/components/ClientLayout.tsx\");\n\n\n\n\nconst metadata = {\n    title: 'OposiAI - Asistente IA para Oposiciones',\n    description: 'Aplicación de preguntas y respuestas con IA para temarios de oposiciones'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"es\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} bg-gray-100`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGlCO0FBQzhCO0FBSTlDLE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdSO0lBQ0EscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVcsR0FBR1YsK0pBQWUsQ0FBQyxZQUFZLENBQUM7c0JBQy9DLDRFQUFDQyxnRUFBWUE7MEJBQ1ZLOzs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSk9SR0VcXERlc2t0b3BcXHRyYWJham9cXG9wb3NpY2lvbmVzXFxzcmNcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCc7XG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnO1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJztcbmltcG9ydCBDbGllbnRMYXlvdXQgZnJvbSAnQC9jb21wb25lbnRzL0NsaWVudExheW91dCc7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnT3Bvc2lBSSAtIEFzaXN0ZW50ZSBJQSBwYXJhIE9wb3NpY2lvbmVzJyxcbiAgZGVzY3JpcHRpb246ICdBcGxpY2FjacOzbiBkZSBwcmVndW50YXMgeSByZXNwdWVzdGFzIGNvbiBJQSBwYXJhIHRlbWFyaW9zIGRlIG9wb3NpY2lvbmVzJyxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVzXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2Ake2ludGVyLmNsYXNzTmFtZX0gYmctZ3JheS0xMDBgfT5cbiAgICAgICAgPENsaWVudExheW91dD5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvQ2xpZW50TGF5b3V0PlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIkNsaWVudExheW91dCIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\trabajo\\oposiciones\\src\\app\\login\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ClientLayout.tsx":
/*!*****************************************!*\
  !*** ./src/components/ClientLayout.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\trabajo\\\\oposiciones\\\\src\\\\components\\\\ClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\trabajo\\oposiciones\\src\\components\\ClientLayout.tsx",
"default",
));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CJORGE%5CDesktop%5Ctrabajo%5Coposiciones%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJORGE%5CDesktop%5Ctrabajo%5Coposiciones&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();